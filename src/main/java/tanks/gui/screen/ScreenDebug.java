package tanks.gui.screen;

import tanks.Drawing;
import tanks.Game;
import tanks.gui.Button;
import tanks.replay.ReplayIO;
import tanks.tank.TankPlayer;
import java.util.function.BooleanSupplier;
import java.util.function.Consumer;

public class ScreenDebug extends Screen
{
    // Inner class to handle button registry
    private static class ButtonRegistry
    {
        public Button button;
        public String labelText;
        public BooleanSupplier getter;
        public Consumer<Boolean> setter;

        public ButtonRegistry(double x, double y, double width, double height, String labelText,
                            BooleanSupplier getter, Consumer<Boolean> setter)
        {
            this.labelText = labelText;
            this.getter = getter;
            this.setter = setter;

            this.button = new Button(x, y, width, height, "", () -> {
                boolean currentValue = this.getter.getAsBoolean();
                this.setter.accept(!currentValue);
                this.updateText();
            });

            this.updateText();
        }

        public void updateText()
        {
            if (this.getter.getAsBoolean())
                this.button.setText(this.labelText, ScreenOptions.onText);
            else
                this.button.setText(this.labelText, ScreenOptions.offText);
        }

        public void update()
        {
            this.button.update();
        }

        public void draw()
        {
            this.button.draw();
        }
    }

    // Button registry array
    private ButtonRegistry[] buttonRegistry;

    public ScreenDebug()
    {
        this.music = "menu_options.ogg";
        this.musicID = "menu";

        if (Game.traceAllRays)
            traceAllRays.setText(traceText, ScreenOptions.onText);
        else
            traceAllRays.setText(traceText, ScreenOptions.offText);

        if (Game.firstPerson)
            firstPerson.setText(firstPersonText, ScreenOptions.onText);
        else
            firstPerson.setText(firstPersonText, ScreenOptions.offText);

        if (Game.followingCam)
            followingCam.setText(followingCamText, ScreenOptions.onText);
        else
            followingCam.setText(followingCamText, ScreenOptions.offText);

        showPathfinding.setText(showPathfindingText, Game.showPathfinding ? ScreenOptions.onText : ScreenOptions.offText);

        if (Game.showTankIDs)
            tankIDs.setText(tankIDsText, ScreenOptions.onText);
        else
            tankIDs.setText(tankIDsText, ScreenOptions.offText);

        if (Game.invulnerable)
            invulnerable.setText(invulnerableText, ScreenOptions.onText);
        else
            invulnerable.setText(invulnerableText, ScreenOptions.offText);

        if (Game.fancyLights)
            fancyLighting.setText(fancyLightsText, ScreenOptions.onText);
        else
            fancyLighting.setText(fancyLightsText, ScreenOptions.offText);

        if (TankPlayer.enableDestroyCheat)
            destroyCheat.setText(destroyCheatText, ScreenOptions.onText);
        else
            destroyCheat.setText(destroyCheatText, ScreenOptions.offText);

        if (Game.showHitboxes)
            drawFaces.setText(facesText, ScreenOptions.onText);
        else
            drawFaces.setText(facesText, ScreenOptions.offText);
    }

    Button back = new Button(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 4, this.objWidth, this.objHeight, "Back", () -> Game.screen = new ScreenTitle());

    Button test = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 1.5, this.objWidth, this.objHeight, "Test stuff", () -> Game.screen = new ScreenTestDebug());

    Button traceAllRays = new Button(Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 1.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.traceAllRays = !Game.traceAllRays;

            if (Game.traceAllRays)
                traceAllRays.setText(traceText, ScreenOptions.onText);
            else
                traceAllRays.setText(traceText, ScreenOptions.offText);
        }
    });

    Button firstPerson = new Button(Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 0.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.firstPerson = !Game.firstPerson;

            if (Game.firstPerson)
                firstPerson.setText(firstPersonText, ScreenOptions.onText);
            else
                firstPerson.setText(firstPersonText, ScreenOptions.offText);
        }
    });

    Button followingCam = new Button(Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 0.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.followingCam = !Game.followingCam;

            if (Game.followingCam)
                followingCam.setText(followingCamText, ScreenOptions.onText);
            else
                followingCam.setText(followingCamText, ScreenOptions.offText);
        }
    });

    Button showPathfinding = new Button(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 2.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.showPathfinding = !Game.showPathfinding;

            if (Game.showPathfinding)
                showPathfinding.setText(showPathfindingText, ScreenOptions.onText);
            else
                showPathfinding.setText(showPathfindingText, ScreenOptions.offText);
        }
    });

    Button tankIDs = new Button(Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 1.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.showTankIDs = !Game.showTankIDs;

            if (Game.showTankIDs)
                tankIDs.setText(tankIDsText, ScreenOptions.onText);
            else
                tankIDs.setText(tankIDsText, ScreenOptions.offText);
        }
    });

    Button invulnerable = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 0.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.invulnerable = !Game.invulnerable;

            if (Game.invulnerable)
                invulnerable.setText(invulnerableText, ScreenOptions.onText);
            else
                invulnerable.setText(invulnerableText, ScreenOptions.offText);
        }
    });

    Button fancyLighting = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 1.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.fancyLights = !Game.fancyLights;

            if (Game.fancyLights)
                fancyLighting.setText(fancyLightsText, ScreenOptions.onText);
            else
                fancyLighting.setText(fancyLightsText, ScreenOptions.offText);
        }
    });

    Button destroyCheat = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 0.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            TankPlayer.enableDestroyCheat = !TankPlayer.enableDestroyCheat;

            if (TankPlayer.enableDestroyCheat)
                destroyCheat.setText(destroyCheatText, ScreenOptions.onText);
            else
                destroyCheat.setText(destroyCheatText, ScreenOptions.offText);
        }
    });

    Button drawFaces = new Button(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 2.5, this.objWidth, this.objHeight, "", new Runnable()
    {
        @Override
        public void run()
        {
            Game.showHitboxes = !Game.showHitboxes;
            if (Game.showHitboxes)
                drawFaces.setText(facesText, ScreenOptions.onText);
            else
                drawFaces.setText(facesText, ScreenOptions.offText);
        }
    });




    @Override
    public void update()
    {
        test.update();
        traceAllRays.update();
        followingCam.update();
        firstPerson.update();
        showPathfinding.update();
        invulnerable.update();
        tankIDs.update();
        fancyLighting.update();
        destroyCheat.update();
        drawFaces.update();
        back.update();
    }

    @Override
    public void draw()
    {
        this.drawDefaultBackground();
        Drawing.drawing.setInterfaceFontSize(this.titleSize);
        Drawing.drawing.setColor(0, 0, 0);
        Drawing.drawing.displayInterfaceText(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 - 210, "Debug menu");

        firstPerson.draw();
        followingCam.draw();
        test.draw();
        traceAllRays.draw();
        showPathfinding.draw();
        tankIDs.draw();
        invulnerable.draw();
        fancyLighting.draw();
        destroyCheat.draw();
        drawFaces.draw();
        back.draw();
    }

    @Override
    public void onFilesDropped(String... filePaths)
    {
        ReplayIO.read(filePaths[0]).loadAndPlay();
    }
}
