package tanks.gui.screen;

import tanks.Drawing;
import tanks.Game;
import tanks.gui.Button;
import tanks.replay.ReplayIO;
import tanks.tank.TankPlayer;

import java.util.function.BooleanSupplier;
import java.util.function.Consumer;
import java.util.ArrayList;

public class ScreenDebug extends Screen
{
    // Inner class to handle button registry
    private static class ButtonRegistry
    {
        public Button button;
        public String labelText;
        public BooleanSupplier getter;
        public Consumer<Boolean> setter;

        public ButtonRegistry(double x, double y, double width, double height, String labelText,
                            BooleanSupplier getter, Consumer<Boolean> setter)
        {
            this.labelText = labelText;
            this.getter = getter;
            this.setter = setter;

            this.button = new Button(x, y, width, height, "", () -> {
                boolean currentValue = this.getter.getAsBoolean();
                this.setter.accept(!currentValue);
                this.updateText();
            });

            this.updateText();
        }

        public void updateText()
        {
            if (this.getter.getAsBoolean())
                this.button.setText(this.labelText, ScreenOptions.onText);
            else
                this.button.setText(this.labelText, ScreenOptions.offText);
        }

        public void update()
        {
            this.button.update();
        }

        public void draw()
        {
            this.button.draw();
        }
    }

    // Button registry ArrayList
    private ArrayList<ButtonRegistry> buttonRegistry;

    Button back = new Button(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 4, this.objWidth, this.objHeight, "Back", () -> Game.screen = new ScreenTitle());
    Button test = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 1.5, this.objWidth, this.objHeight, "Test stuff", () -> Game.screen = new ScreenTestDebug());

    public ScreenDebug()
    {
        this.music = "menu_options.ogg";
        this.musicID = "menu";

        // Initialize button registry ArrayList
        this.buttonRegistry = new ArrayList<>();

        // Create individual ButtonRegistry objects with automatic layout
        int index = 0;

        // Destroy cheat button
        double yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        double xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Destroy cheat: ",
            () -> TankPlayer.enableDestroyCheat, (value) -> TankPlayer.enableDestroyCheat = value));
        index++;

        // First person button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "First person: ",
            () -> Game.firstPerson, (value) -> Game.firstPerson = value));
        index++;

        // Invulnerable button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Invulnerable: ",
            () -> Game.invulnerable, (value) -> Game.invulnerable = value));
        index++;

        // Immersive camera button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Immersive camera: ",
            () -> Game.followingCam, (value) -> Game.followingCam = value));
        index++;

        // Fancy lighting button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Fancy lighting: ",
            () -> Game.fancyLights, (value) -> Game.fancyLights = value));
        index++;

        // Show tank IDs button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Show tank IDs: ",
            () -> Game.showTankIDs, (value) -> Game.showTankIDs = value));
        index++;

        // Show pathfinding button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Show pathfinding: ",
            () -> Game.showPathfinding, (value) -> Game.showPathfinding = value));
        index++;

        // Draw faces button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Draw faces: ",
            () -> Game.showHitboxes, (value) -> Game.showHitboxes = value));
        index++;

        // Immutable faces button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Immutable faces: ",
            () -> Game.immutableFaces, (value) -> Game.immutableFaces = value));
        index++;

        // Trace rays button
        yPos = Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * (-0.5 + index * 0.5);
        xPos = (index % 2 == 0) ? Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2 : Drawing.drawing.interfaceSizeX / 2 + this.objXSpace / 2;
        this.buttonRegistry.add(new ButtonRegistry(xPos, yPos, this.objWidth, this.objHeight, "Trace rays: ",
            () -> Game.traceAllRays, (value) -> Game.traceAllRays = value));
    }

    @Override
    public void update()
    {
        for (ButtonRegistry registry : buttonRegistry)
            registry.update();

        test.update();
        back.update();
    }

    @Override
    public void draw()
    {
        this.drawDefaultBackground();
        Drawing.drawing.setInterfaceFontSize(this.titleSize);
        Drawing.drawing.setColor(0, 0, 0);
        Drawing.drawing.displayInterfaceText(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 - 210, "Debug menu");

        for (ButtonRegistry registry : buttonRegistry)
            registry.draw();

        test.draw();
        back.draw();
    }

    @Override
    public void onFilesDropped(String... filePaths)
    {
        ReplayIO.read(filePaths[0]).loadAndPlay();
    }
}
